<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bounce Rotation Test - Coin Flipper</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        #coinCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1em;
        }

        .status.idle {
            background: #e8f5e8;
            color: #2d5a2d;
            border: 2px solid #4caf50;
        }

        .status.flipping {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }

        .status.result {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }

        .info-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .info-panel h3 {
            margin-top: 0;
            color: #495057;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }

        .highlight strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Bounce Rotation Test</h1>
        <p class="subtitle">ทดสอบการหมุนในช่วงเด้ง และหยุดเมื่อความสูงต่ำกว่าความสูงเหรียญ</p>

        <canvas id="coinCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <button id="flipHeads">ทอย หัว (2 เด้ง)</button>
            <button id="flipTails">ทอย ก้อย (2 เด้ง)</button>
            <button id="flipRandom">ทอยสุ่ม</button>
        </div>

        <div id="status" class="status idle">พร้อมทดสอบระบบการหมุนในช่วงเด้ง</div>

        <div class="highlight">
            <strong>🆕 การเปลี่ยนแปลงใหม่:</strong> เหรียญจะหมุนต่อไปในช่วงเด้ง และจะหยุดหมุนเมื่อความสูงการเด้งต่ำกว่าความสูงของเหรียญ (0.1) แล้วแสดงผลลัพธ์ทันที
        </div>

        <div class="info-panel">
            <h3>🔧 ระบบการทำงานใหม่</h3>
            <ul class="feature-list">
                <li><strong>ช่วงขึ้น:</strong> หมุนด้วยความเร็ว 1.0 radians/frame</li>
                <li><strong>ช่วงลง:</strong> หมุนต่อไปด้วยความเร็วเดิม</li>
                <li><strong>ช่วงเด้ง:</strong> หมุนด้วยความเร็ว 0.8 radians/frame (ช้าลงเล็กน้อย)</li>
                <li><strong>เงื่อนไขหยุด:</strong> เมื่อความสูงการเด้งต่อไป < ความสูงเหรียญ (0.1)</li>
                <li><strong>ป้องกันจมพื้น:</strong> ระบบ collision detection ยังคงทำงาน</li>
                <li><strong>แสดงผลลัพธ์:</strong> ทันทีเมื่อหยุดหมุน</li>
            </ul>
        </div>

        <div class="info-panel">
            <h3>📊 การตั้งค่าปัจจุบัน</h3>
            <ul class="feature-list">
                <li><strong>ความสูงเหรียญ:</strong> 0.1 units</li>
                <li><strong>จำนวนเด้งเป้าหมาย:</strong> 2 ครั้ง (ทั้งหัวและก้อย)</li>
                <li><strong>ความสูงโยน:</strong> หัว = 4.0, ก้อย = 4.0</li>
                <li><strong>ความเร็วหมุน:</strong> 1.0 radians/frame</li>
            </ul>
        </div>

        <div class="info-panel">
            <h3>🎮 วิธีทดสอบ</h3>
            <ol>
                <li>กดปุ่มทอยเหรียญ</li>
                <li>สังเกตการหมุนในช่วงขึ้น-ลง-เด้ง</li>
                <li>ดูว่าเหรียญหยุดหมุนเมื่อเด้งต่ำ</li>
                <li>ตรวจสอบ Console เพื่อดู log การทำงาน</li>
                <li>ยืนยันว่าเหรียญไม่จมพื้น</li>
            </ol>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Coin Flipper Module -->
    <script src="../src/coin-flipper.js"></script>

    <script>
        let coinFlipper;
        let flipCount = 0;

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        // Initialize coin flipper
        async function init() {
            try {
                const canvas = document.getElementById('coinCanvas');
                coinFlipper = new CoinFlipper(canvas);
                
                updateStatus('idle', 'พร้อมทดสอบระบบการหมุนในช่วงเด้ง');
                console.log('✅ Coin flipper initialized successfully');
                console.log('🔧 Current settings:', {
                    coinHeight: coinFlipper.coinRenderer.coinHeight,
                    targetBounces: coinFlipper.coinRenderer.targetBounces,
                    maxFlipHeight: coinFlipper.coinRenderer.maxFlipHeight,
                    flipRotationSpeed: coinFlipper.coinRenderer.flipRotationSpeed
                });
            } catch (error) {
                console.error('❌ Failed to initialize coin flipper:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาดในการเริ่มต้น');
            }
        }

        // Event listeners
        document.getElementById('flipHeads').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอย หัว (ครั้งที่ ${flipCount}) - ดูการหมุนในช่วงเด้ง`);
            
            try {
                const result = await coinFlipper.flip('heads');
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ทดสอบเสร็จสิ้น`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 2000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        document.getElementById('flipTails').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอย ก้อย (ครั้งที่ ${flipCount}) - ดูการหมุนในช่วงเด้ง`);
            
            try {
                const result = await coinFlipper.flip('tails');
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ทดสอบเสร็จสิ้น`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 2000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        document.getElementById('flipRandom').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอยสุ่ม (ครั้งที่ ${flipCount}) - ดูการหมุนในช่วงเด้ง`);
            
            try {
                const result = await coinFlipper.flip();
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ทดสอบเสร็จสิ้น`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 2000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
