<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Height & Rotation Speed Test - Coin Flipper</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        #coinCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1em;
        }

        .status.idle {
            background: #e8f5e8;
            color: #2d5a2d;
            border: 2px solid #4caf50;
        }

        .status.flipping {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }

        .status.result {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }

        .info-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .info-panel h3 {
            margin-top: 0;
            color: #495057;
        }

        .pattern-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .pattern-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .pattern-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .pattern-card .value {
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Height & Rotation Speed Test</h1>
        <p class="subtitle">ทดสอบระบบความสูงและความเร็วหมุนแทน gravity และ damping</p>

        <canvas id="coinCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <button id="flipHeads">ทอย หัว (สูง + เร็ว)</button>
            <button id="flipTails">ทอย ก้อย (ต่ำ + ช้า)</button>
            <button id="flipRandom">ทอยสุ่ม</button>
        </div>

        <div id="status" class="status idle">พร้อมทดสอบระบบใหม่</div>

        <div class="info-panel">
            <h3>📊 การตั้งค่าปัจจุบัน</h3>
            <div class="pattern-info">
                <div class="pattern-card">
                    <h4>🎯 หัว (Heads)</h4>
                    <p>ความสูง: <span class="value">4.0</span></p>
                    <p>ความเร็วหมุน: <span class="value">0.9</span></p>
                    <p>จำนวนเด้ง: <span class="value">8</span></p>
                </div>
                <div class="pattern-card">
                    <h4>🎯 ก้อย (Tails)</h4>
                    <p>ความสูง: <span class="value">3.2</span></p>
                    <p>ความเร็วหมุน: <span class="value">0.7</span></p>
                    <p>จำนวนเด้ง: <span class="value">7</span></p>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h3>🔧 การเปลี่ยนแปลง</h3>
            <ul style="text-align: left;">
                <li><strong>เก่า:</strong> ใช้ gravity และ damping ในการควบคุมการเด้ง</li>
                <li><strong>ใหม่:</strong> ใช้ความสูงและความเร็วหมุนที่แตกต่างกันตามผลลัพธ์</li>
                <li><strong>ป้องกัน:</strong> เหรียญไม่จมพื้นด้วยระบบ collision detection</li>
                <li><strong>ผลลัพธ์:</strong> การโยนแต่ละแบบมีลักษณะเฉพาะที่แตกต่างกัน</li>
            </ul>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Coin Flipper Module -->
    <script src="../src/coin-flipper.js"></script>

    <script>
        let coinFlipper;
        let flipCount = 0;

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        // Initialize coin flipper
        async function init() {
            try {
                const canvas = document.getElementById('coinCanvas');
                coinFlipper = new CoinFlipper(canvas);
                
                updateStatus('idle', 'พร้อมทดสอบระบบใหม่');
                console.log('✅ Coin flipper initialized successfully');
            } catch (error) {
                console.error('❌ Failed to initialize coin flipper:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาดในการเริ่มต้น');
            }
        }

        // Event listeners
        document.getElementById('flipHeads').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอย หัว (ครั้งที่ ${flipCount}) - สูงและเร็ว`);
            
            try {
                const result = await coinFlipper.flip('heads');
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ทดสอบเสร็จสิ้น`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 2000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        document.getElementById('flipTails').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอย ก้อย (ครั้งที่ ${flipCount}) - ต่ำและช้า`);
            
            try {
                const result = await coinFlipper.flip('tails');
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ทดสอบเสร็จสิ้น`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 2000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        document.getElementById('flipRandom').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอยสุ่ม (ครั้งที่ ${flipCount}) - ระบบเลือกอัตโนมัติ`);
            
            try {
                const result = await coinFlipper.flip();
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ทดสอบเสร็จสิ้น`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 2000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
