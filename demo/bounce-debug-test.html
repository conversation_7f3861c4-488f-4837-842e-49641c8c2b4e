<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bounce Debug Test - Coin Flipper</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 900px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        #coinCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1em;
        }

        .status.idle {
            background: #e8f5e8;
            color: #2d5a2d;
            border: 2px solid #4caf50;
        }

        .status.flipping {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }

        .status.result {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #17a2b8;
        }

        .debug-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .debug-panel h3 {
            margin-top: 0;
            color: #495057;
        }

        .debug-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .debug-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .debug-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .debug-card .value {
            font-weight: bold;
            color: #667eea;
            font-family: monospace;
        }

        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }

        .highlight strong {
            color: #856404;
        }

        .console-note {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
            margin: 15px 0;
        }

        .console-note strong {
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Bounce Debug Test</h1>
        <p class="subtitle">ทดสอบและ debug ปัญหาการเด้งของเหรียญ</p>

        <canvas id="coinCanvas" width="400" height="400"></canvas>

        <div class="controls">
            <button id="flipHeads">ทอย หัว</button>
            <button id="flipTails">ทอย ก้อย</button>
            <button id="flipRandom">ทอยสุ่ม</button>
        </div>

        <div id="status" class="status idle">พร้อมทดสอบการเด้ง</div>

        <div class="highlight">
            <strong>🔧 การแก้ไข:</strong> เปลี่ยนเงื่อนไขให้เหรียญเด้งได้อย่างน้อย 1 ครั้งก่อนตรวจสอบความสูง
        </div>

        <div class="console-note">
            <strong>📋 คำแนะนำ:</strong> เปิด Developer Console (F12) เพื่อดู log การทำงานของระบบการเด้ง
        </div>

        <div class="debug-panel">
            <h3>🔧 การตั้งค่าปัจจุบัน</h3>
            <div class="debug-info">
                <div class="debug-card">
                    <h4>⚙️ Physics</h4>
                    <p>Gravity: <span class="value" id="gravityValue">-0.025</span></p>
                    <p>Bounce Damping: <span class="value" id="dampingValue">0.7</span></p>
                    <p>Ground Y: <span class="value" id="groundYValue">-1.5</span></p>
                </div>
                <div class="debug-card">
                    <h4>🪙 Coin Properties</h4>
                    <p>Coin Height: <span class="value" id="coinHeightValue">0.1</span></p>
                    <p>Coin Radius: <span class="value" id="coinRadiusValue">0.8</span></p>
                    <p>Target Bounces: <span class="value" id="targetBouncesValue">2</span></p>
                </div>
                <div class="debug-card">
                    <h4>🎯 Animation</h4>
                    <p>Max Height: <span class="value" id="maxHeightValue">4.0</span></p>
                    <p>Rotation Speed: <span class="value" id="rotationSpeedValue">0.4</span></p>
                    <p>Flip Duration: <span class="value" id="flipDurationValue">3000ms</span></p>
                </div>
                <div class="debug-card">
                    <h4>🔄 Current State</h4>
                    <p>Phase: <span class="value" id="currentPhase">idle</span></p>
                    <p>Bounce Count: <span class="value" id="bounceCount">0</span></p>
                    <p>Position Y: <span class="value" id="positionY">0</span></p>
                </div>
            </div>
        </div>

        <div class="debug-panel">
            <h3>🎯 สิ่งที่ต้องสังเกต</h3>
            <ul>
                <li><strong>Phase Transitions:</strong> idle → ascending → descending → bouncing</li>
                <li><strong>Bounce Detection:</strong> ดูว่าเหรียญเข้าสู่ bouncing phase หรือไม่</li>
                <li><strong>Bounce Count:</strong> ตรวจสอบว่าเหรียญเด้งได้กี่ครั้ง</li>
                <li><strong>Height Check:</strong> ดูว่าการตรวจสอบความสูงทำงานถูกต้องหรือไม่</li>
                <li><strong>Force Flat:</strong> ดูว่า forceFlat() ถูกเรียกเมื่อไหร่</li>
            </ul>
        </div>

        <div class="debug-panel">
            <h3>🐛 การแก้ไขปัญหา</h3>
            <p><strong>ปัญหาเดิม:</strong> เหรียญไม่เด้งเพราะ forceFlat() ถูกเรียกทันทีเมื่อเข้าสู่ bouncing phase</p>
            <p><strong>การแก้ไข:</strong> เปลี่ยนเงื่อนไขเป็น <code>bounceCount >= 1 && currentBounceHeight <= coinHeight</code></p>
            <p><strong>ผลลัพธ์ที่คาดหวัง:</strong> เหรียญจะเด้งได้อย่างน้อย 1 ครั้งก่อนหยุด</p>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Coin Flipper Module -->
    <script src="../src/coin-flipper.js"></script>

    <script>
        let coinFlipper;
        let flipCount = 0;
        let debugInterval;

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function updateDebugInfo() {
            if (!coinFlipper || !coinFlipper.coinRenderer) return;

            const renderer = coinFlipper.coinRenderer;
            
            // Update debug values
            document.getElementById('gravityValue').textContent = renderer.gravity.toFixed(3);
            document.getElementById('dampingValue').textContent = renderer.bounceDamping.toFixed(2);
            document.getElementById('groundYValue').textContent = renderer.groundY.toFixed(1);
            document.getElementById('coinHeightValue').textContent = renderer.coinHeight.toFixed(1);
            document.getElementById('coinRadiusValue').textContent = renderer.coinRadius.toFixed(1);
            document.getElementById('targetBouncesValue').textContent = renderer.targetBounces;
            document.getElementById('maxHeightValue').textContent = renderer.maxFlipHeight.toFixed(1);
            document.getElementById('rotationSpeedValue').textContent = renderer.flipRotationSpeed.toFixed(1);
            document.getElementById('flipDurationValue').textContent = renderer.options.flipDuration + 'ms';
            document.getElementById('currentPhase').textContent = renderer.flipPhase || 'idle';
            document.getElementById('bounceCount').textContent = renderer.bounceCount || 0;
            document.getElementById('positionY').textContent = (renderer.coinPositionY || 0).toFixed(3);
        }

        // Initialize coin flipper
        async function init() {
            try {
                const canvas = document.getElementById('coinCanvas');
                coinFlipper = new CoinFlipper(canvas);
                
                updateStatus('idle', 'พร้อมทดสอบการเด้ง');
                console.log('✅ Coin flipper initialized successfully');
                
                // Start debug info updates
                debugInterval = setInterval(updateDebugInfo, 100);
                updateDebugInfo();
                
            } catch (error) {
                console.error('❌ Failed to initialize coin flipper:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาดในการเริ่มต้น');
            }
        }

        // Event listeners
        document.getElementById('flipHeads').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอย หัว (ครั้งที่ ${flipCount}) - ดูการเด้ง`);
            
            try {
                const result = await coinFlipper.flip('heads');
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ตรวจสอบ Console`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 3000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        document.getElementById('flipTails').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอย ก้อย (ครั้งที่ ${flipCount}) - ดูการเด้ง`);
            
            try {
                const result = await coinFlipper.flip('tails');
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ตรวจสอบ Console`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 3000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        document.getElementById('flipRandom').addEventListener('click', async () => {
            if (coinFlipper.isFlipping) return;
            
            flipCount++;
            updateStatus('flipping', `กำลังทอยสุ่ม (ครั้งที่ ${flipCount}) - ดูการเด้ง`);
            
            try {
                const result = await coinFlipper.flip();
                updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'} - ตรวจสอบ Console`);
                
                setTimeout(() => {
                    updateStatus('idle', 'พร้อมทดสอบครั้งต่อไป');
                }, 3000);
            } catch (error) {
                console.error('Flip error:', error);
                updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทดสอบใหม่');
            }
        });

        // Initialize when page loads
        window.addEventListener('load', init);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (debugInterval) {
                clearInterval(debugInterval);
            }
        });
    </script>
</body>
</html>
